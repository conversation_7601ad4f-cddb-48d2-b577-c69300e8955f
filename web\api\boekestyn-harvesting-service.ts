import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from './api-base';
import * as models from './models/boekestyns';

export const boekestynHarvestingApi = createApi({
  reducerPath: 'boekestyn-harvesting-api',
  baseQuery: axiosBaseQuery('boekestyns/harvesting/'),
  refetchOnMountOrArgChange: true,
  tagTypes: ['HarvestingSchedules', 'HarvestingWorkOrdersByDate'],
  endpoints: (builder) => ({
    harvesting: builder.query<BoekestynHarvestingResponse, void>({
      query: () => ({
        url: '',
      }),
    }),
    harvestingOrders: builder.query<
      BoekestynHarvestingOrdersResponse,
      BoekestynHarvestingOrdersArgs
    >({
      query: ({ startDate, endDate }) => ({
        url: `orders?startDate=${startDate}&endDate=${endDate}`,
      }),
    }),
    harvestingSchedules: builder.query<
      BoekestynHarvestingSchedulesResponse,
      BoekestynHarvestingSchedulesArgs
    >({
      query: ({ date }) => ({
        url: `schedules?date=${date}`,
      }),
      providesTags: ['HarvestingSchedules'],
    }),
    addHarvestingOrderToHarvestingSchedule: builder.mutation<
      void,
      AddHarvestingOrderToHarvestingScheduleArgs
    >({
      query: (data) => ({
        url: `workOrders`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['HarvestingSchedules'],
    }),
    sortHarvestingWorkOrders: builder.mutation<
      void,
      SortHarvestingWorkOrdersArgs
    >({
      query: (data) => ({
        url: `workOrders/sort`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['HarvestingSchedules'],
    }),
    updateHarvestingWorkOrderComment: builder.mutation<
      void,
      UpdateHarvestingWorkOrderCommentArgs
    >({
      query: ({ id, comment }) => ({
        url: `workOrders/${id}/comment`,
        method: 'POST',
        data: { comment },
      }),
      invalidatesTags: ['HarvestingSchedules'],
    }),
    deleteHarvestingWorkOrder: builder.mutation<
      void,
      DeleteHarvestingWorkOrderArgs
    >({
      query: (data) => ({
        url: `workOrders/${data.id}/remove`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['HarvestingSchedules'],
    }),
    getHarvestingWorkOrdersByDate: builder.query<
      HarvestingWorkOrdersByDateResponse,
      string
    >({
      query: (date) => ({
        url: `workOrders?date=${date}`,
      }),
      providesTags: ['HarvestingWorkOrdersByDate'],
    }),
    startHarvestingWorkOrderLabour: builder.mutation<
      void,
      StartHarvestingWorkOrderLabourArgs
    >({
      query: (data) => ({
        url: `workOrders/${data.workOrderId}/labour/start`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['HarvestingWorkOrdersByDate'],
    }),
    pauseHarvestingWorkOrderLabour: builder.mutation<
      void,
      PauseHarvestingWorkOrderLabourArgs
    >({
      query: (data) => ({
        url: `workOrders/${data.workOrderId}/labour/pause`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['HarvestingWorkOrdersByDate'],
    }),
    stopHarvestingWorkOrderLabour: builder.mutation<
      void,
      StopHarvestingWorkOrderLabourArgs
    >({
      query: (data) => ({
        url: `workOrders/${data.workOrderId}/labour/stop`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['HarvestingWorkOrdersByDate'],
    }),
  }),
});

export interface BoekestynHarvestingResponse {
  lines: models.HarvestingLine[];
}

interface BoekestynHarvestingOrdersArgs {
  startDate: string;
  endDate: string;
}

export interface BoekestynHarvestingOrdersResponse {
  orders: models.HarvestingOrder[];
}

interface BoekestynHarvestingSchedulesArgs {
  date: string;
}

export interface BoekestynHarvestingSchedulesResponse {
  schedules: models.HarvestingSchedule[];
}

export interface BoekestynHarvestingLinesResponse {
  lines: models.HarvestingLine[];
}

export interface AddHarvestingOrderToHarvestingScheduleArgs {
  crewSize: number;
  comments: string | null;
  schedule: models.HarvestingSchedule;
  order: models.HarvestingOrder;
  varieties: models.HarvestingOrderVariety[];
  maximumHarvestRounds: number;
  daysBetweenHarvestRounds: number;
  nextRoundDate: string | null;
  finalRound: boolean;
}

export interface DeleteHarvestingWorkOrderArgs {
  id: number;
  orderId: string;
}

export interface SortHarvestingWorkOrdersArgs {
  workOrders: { workOrderId: number; sortOrder: number }[];
}

export interface UpdateHarvestingWorkOrderCommentArgs {
  id: number;
  comment: string | null;
}

export interface HarvestingWorkOrdersByDateResponse {
  orders: models.HarvestingWorkOrderItem[];
}

export interface StartHarvestingWorkOrderLabourArgs {
  workOrderId: number;
  crewSize: number;
}

export interface PauseHarvestingWorkOrderLabourArgs {
  workOrderId: number;
  crewSize: number;
  comments: string | null;
}

export interface HarvestingLabourVariety {
  varietyName: string;
  harvested: number;
  thrownOut: number;
}

export interface StopHarvestingWorkOrderLabourArgs {
  workOrderId: number;
  crewSize: number;
  comments: string | null;
  labourVarieties: HarvestingLabourVariety[];
}

export const {
  useHarvestingQuery,
  useHarvestingOrdersQuery,
  useHarvestingSchedulesQuery,
  useAddHarvestingOrderToHarvestingScheduleMutation,
  useSortHarvestingWorkOrdersMutation,
  useDeleteHarvestingWorkOrderMutation,
  useUpdateHarvestingWorkOrderCommentMutation,
  useGetHarvestingWorkOrdersByDateQuery,
  useStartHarvestingWorkOrderLabourMutation,
  usePauseHarvestingWorkOrderLabourMutation,
  useStopHarvestingWorkOrderLabourMutation,
} = boekestynHarvestingApi;
