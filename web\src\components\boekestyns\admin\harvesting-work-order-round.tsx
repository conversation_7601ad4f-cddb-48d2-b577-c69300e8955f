import { Icon } from "@/components/icon";
import { formatNumber } from "@/utils/format";
import { HarvestingOrder, HarvestingOrderRound, HarvestingWorkOrder } from "api/models/boekestyns";
import moment from 'moment';
import { useState } from "react";
import * as HeadlessUI from '@headlessui/react';
import { classNames } from '@/utils/class-names';

export type HarvestingWorkOrderDayProps = {
  workOrder: Partial<HarvestingWorkOrder>;
  allWorkOrders: { [key: string]: Partial<HarvestingWorkOrder>; };
  order: HarvestingOrder | null;
  date: string;
};

export function HarvestingWorkOrderDay({ workOrder, allWorkOrders, order }: HarvestingWorkOrderDayProps) {
  //const [order, setorder] =
  //  useState<HarvestingOrder | null>(null),
  const [crewSize, setCrewSize] = useState(1),
    [finalRound, setFinalRound] = useState(false),
    [comments, setComments] = useState<string | null>(null),
    [nextHarvestDate, setNextHarvestDate] = useState<string | null>(null),
    [selectedVarieties, setSelectedVarieties] = useState<{[key: string]: boolean} | null>(null);


   const getVarietyHarvestedCount = (variety: string) => {
    return allRounds.reduce((total, round) => {
      return (
        total +
        (round.varieties ? round.varieties
          .filter((v) => !variety || variety === v.name)
          .reduce((sum, v) => v.harvested + sum, 0) : 0)
      );
    }, 0);
  };*/
  /*const getVarietyHarvestedCount = (variety: string) => {
    return Object.keys(allWorkOrders).reduce((total, date) => {
      return (
        total +
        (allWorkOrders[date].varieties ? allWorkOrders[date].varieties
          .filter((v) => !variety || variety === v.name)
          .reduce((sum, v) => v.harvested + sum, 0) : 0)
      );
    }, 0);
  };

  const getVarietyThrownOutCount = (variety: string) => {
    return Object.keys(allWorkOrders).reduce((total, date) => {
      return (
        total +
        (allWorkOrders[date].varieties ? allWorkOrders[date].varieties
          .filter((v) => !variety || variety === v.name)
          .reduce((sum, v) => v.thrownOut + sum, 0) : 0)
      );
    }, 0);
  };

  const handleSelectVarietyChanged = (checked: boolean, variety: string) => {
    setSelectedVarieties({
      ...selectedVarieties,
      [variety]: checked,
    });
  }

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">
        Work Order for {moment(date).format('dddd, MMMM D, YYYY')}
      </h3>

      <div className="space-y-4">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-700 mb-2">Round Details</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Date:</span>
              <span className="ml-2 font-medium">{date}</span>
            </div>
            {workOrder.roundNumber && (
              <div>
                <span className="text-gray-500">Round Number:</span>
                <span className="ml-2 font-medium">{workOrder.roundNumber}</span>
              </div>
            )}
            {workOrder.pots && (
              <div>
                <span className="text-gray-500">Pots:</span>
                <span className="ml-2 font-medium">{workOrder.pots}</span>
              </div>
            )}
            {workOrder.cases && (
              <div>
                <span className="text-gray-500">Cases:</span>
                <span className="ml-2 font-medium">{workOrder.cases}</span>
              </div>
            )}
          </div>
        </div>

        {workOrder.varieties && workOrder.varieties.length > 0 && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-700 mb-2">Varieties</h4>
            <div className="space-y-2">
              {workOrder.varieties.map((variety, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span className="font-medium">{variety.name}</span>
                  <span className="text-gray-500">
                    Harvested: {variety.harvested}, Thrown Out: {variety.thrownOut}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="rounded border p-8">
          <h3 className="text-lg font-semibold text-gray-900">
            {order?.orderNumber}
            <div className="italic">
              {order?.plant.name}
            </div>
          </h3>
          <div>
            <table className="min-w-full divide-y divide-gray-300 text-sm">
              <thead>
                <tr>
                  <th className="w-1 p-2">Variety</th>
                  <th className="w-1 p-2 text-right">Planted</th>
                  <th className="w-1 p-2 text-right">Harvested </th>
                  <th className="w-1 p-2 text-right">Thrown Out</th>
                  <th className="w-1 whitespace-nowrap p-2 text-right">
                    To Schedule
                  </th>
                  <th>Expected Percentage</th>
                  <th>&nbsp;</th>
                </tr>
              </thead>
              <tbody>
                {order?.varieties.map((variety) => (
                  <tr key={variety.name}>
                    <td className="w-1 whitespace-nowrap p-2 text-left">
                      {variety.name}
                    </td>
                    <td className="w-1 p-2 text-right">
                      {formatNumber(variety.pots, '0,0')}
                    </td>
                    <td className="w-1 p-2 text-right">
                      {formatNumber(getVarietyHarvestedCount(variety.name), '0,0')}
                    </td>
                    <td className="w-1 p-2 text-right">
                      {formatNumber(getVarietyThrownOutCount(variety.name), '0,0')}
                    </td>
                    <td className="w-1 p-2 text-right">
                      {formatNumber(
                        variety.pots - getVarietyHarvestedCount(variety.name),
                        '0,0'
                      )}
                    </td>
                    <td className="w-1 p-2 text-right">
                      <input
                        type="number"
                        value={}
                        onChange={(e) => handleExpectedHarvestPercentageChange(e, variety.name)}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </td>
                    <td className="text-center">
                      <HeadlessUI.Switch
                        onChange={(checked) =>
                          handleSelectVarietyChanged(checked, variety.name)
                        }
                        className={classNames(
                          selectedVarieties?.[variety.name] ? 'bg-blue-400' : 'bg-gray-200',
                          'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                        )}
                      >
                        <span
                          aria-hidden="true"
                          className={classNames(
                            selectedVarieties?.[variety.name] ? 'translate-x-5' : 'translate-x-0',
                            'pointer-events-none inline-block size-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                          )}
                        />
                      </HeadlessUI.Switch>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="mt-4 grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-500">
                Crew Size
              </label>
              <input
                type="number"
                value={crewSize}
                onChange={(e) => setCrewSize(e.target.valueAsNumber)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-center text-sm font-medium text-gray-500">
                Final Round?
              </label>
              <div className="text-center">
                <HeadlessUI.Switch
                  checked={finalRound}
                  onChange={setFinalRound}
                  className={classNames(
                    finalRound ? 'bg-blue-400' : 'bg-gray-200',
                    'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                  )}
                >
                  <span
                    aria-hidden="true"
                    className={classNames(
                      finalRound ? 'translate-x-5' : 'translate-x-0',
                      'pointer-events-none inline-block size-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                    )}
                      />
                </HeadlessUI.Switch>
              </div>
                  {!finalRound && (
              <div>
                <label className="block text-sm font-medium text-gray-500">
                  Next Harvest Date
                </label>
                <input
                  type="date"
                  value={nextHarvestDate ?? ''}
                  onChange={(e) => setNextHarvestDate(e.target.value)}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            )}
            <div className="col-span-3">
              <label className="block text-sm font-medium text-gray-500">
                Comments
              </label>
              <textarea
                rows={3}
                value={comments ?? ''}
                onChange={(e) => setComments(e.target.value)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  );
}
