import { Icon } from "@/components/icon";
import { formatNumber } from "@/utils/format";
import { HarvestingOrder, HarvestingOrderRound, HarvestingWorkOrder } from "api/models/boekestyns";
import moment from 'moment';
import { useState } from "react";
import * as HeadlessUI from '@headlessui/react';
import { classNames } from '@/utils/class-names';

export type HarvestingWorkOrderDayProps = {
  workOrder: Partial<HarvestingWorkOrder>;
  allWorkOrders: { [key: string]: Partial<HarvestingWorkOrder>; };
  order: HarvestingOrder | null;
  date: string;
};

export function HarvestingWorkOrderDay({ workOrder, allWorkOrders, order, date }: HarvestingWorkOrderDayProps) {
  const [crewSize, setCrewSize] = useState(1),
    [finalRound, setFinalRound] = useState(false),
    [comments, setComments] = useState<string | null>(null),
    [nextHarvestDate, setNextHarvestDate] = useState<string | null>(null),
    [selectedVarieties, setSelectedVarieties] = useState<{[key: string]: boolean} | null>(null),
    [expectedHarvestPercentages, setExpectedHarvestPercentages] = useState<{[key: string]: number}>({});

  // Get harvested count from order rounds (previous harvesting rounds)
  const getVarietyHarvestedCount = (variety: string) => {
    if (!order?.rounds) return 0;
    return order.rounds.reduce((total, round) => {
      return (
        total +
        round.varieties
          .filter((v) => !variety || variety === v.name)
          .reduce((sum, v) => v.harvested + sum, 0)
      );
    }, 0);
  };

  // Get thrown out count from order rounds (previous harvesting rounds)
  const getVarietyThrownOutCount = (variety: string) => {
    if (!order?.rounds) return 0;
    return order.rounds.reduce((total, round) => {
      return (
        total +
        round.varieties
          .filter((v) => !variety || variety === v.name)
          .reduce((sum, v) => v.thrownOut + sum, 0)
      );
    }, 0);
  };

  // Calculate expected percentage based on previous rounds
  const getExpectedHarvestPercentage = (variety: string) => {
    //Get all work orders with dates before current one
    const previousWorkOrders = Object.values(allWorkOrders).filter((wo) => {
      return wo.id !== workOrder.id && wo.scheduleDate && wo.scheduleDate < date;
    });

    // Get all rounds for those work orders
    const previousRounds = previousWorkOrders.reduce((acc, wo) => {
      return acc.concat(wo.rounds ?? []);
    }, [] as HarvestingOrderRound[]);

    // Get all varieties for those rounds
    const previousVarieties = previousRounds.reduce((acc, round) => {
      return acc.concat(round.varieties);
    }, [] as HarvestingOrderRoundVariety[]);

    // Filter to just the variety we're interested in
    const varietyData = previousVarieties.filter((v) => v.name === variety);

    // If no previous data, use default percentage or 0
    if (varietyData.length === 0) {
      return workOrder.defaultExpectedHarvestPercentage ?? 0;
    }

    // Calculate total harvested and total processed
    const totalHarvested = varietyData.reduce((total, v) => total + v.harvested, 0);
    const totalProcessed = varietyData.reduce((total, v) => total + v.harvested + v.thrownOut, 0);

    // Calculate expected percentage



  };

  // Calculate remaining pots using expected percentage
  const getVarietyRemainingPots = (variety: string) => {
    const varietyData = order?.varieties.find(v => v.name === variety);
    if (!varietyData) return 0;

    const totalHarvested = getVarietyHarvestedCount(variety);
    const totalThrownOut = getVarietyThrownOutCount(variety);
    const totalProcessed = totalHarvested + totalThrownOut;
    const remainingPots = varietyData.pots - totalProcessed;

    if (remainingPots <= 0) return 0;

    const expectedPercentage = getExpectedHarvestPercentage(variety);

    // If this is marked as final round, return all remaining pots
    if (finalRound) {
      return remainingPots;
    }

    // Otherwise, calculate expected harvest for this round based on percentage
    return Math.round(remainingPots * (expectedPercentage / 100));
  };

  const handleSelectVarietyChanged = (checked: boolean, variety: string) => {
    setSelectedVarieties({
      ...selectedVarieties,
      [variety]: checked,
    });
  };

  const handleExpectedHarvestPercentageChange = (e: React.ChangeEvent<HTMLInputElement>, variety: string) => {
    const value = e.target.valueAsNumber;
    setExpectedHarvestPercentages({
      ...expectedHarvestPercentages,
      [variety]: isNaN(value) ? 0 : value,
    });
  };

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">
        Work Order for {moment(date).format('dddd, MMMM D, YYYY')}
      </h3>

      <div className="space-y-4">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-700 mb-2">Round Details</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Date:</span>
              <span className="ml-2 font-medium">{date}</span>
            </div>

            {workOrder.pots && (
              <div>
                <span className="text-gray-500">Pots:</span>
                <span className="ml-2 font-medium">{workOrder.pots}</span>
              </div>
            )}
            {workOrder.cases && (
              <div>
                <span className="text-gray-500">Cases:</span>
                <span className="ml-2 font-medium">{workOrder.cases}</span>
              </div>
            )}
          </div>
        </div>

        {workOrder.varieties && workOrder.varieties.length > 0 && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-700 mb-2">Work Order Varieties</h4>
            <div className="space-y-2">
              {workOrder.varieties.map((variety, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span className="font-medium">{variety.name}</span>
                  <span className="text-gray-500">
                    Pots: {variety.pots}, Cases: {variety.cases}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="rounded border p-8">
          <h3 className="text-lg font-semibold text-gray-900">
            {order?.orderNumber}
            <div className="italic">
              {order?.plant.name}
            </div>
          </h3>
          <div>
            <table className="min-w-full divide-y divide-gray-300 text-sm">
              <thead>
                <tr>
                  <th className="w-1 p-2">Variety</th>
                  <th className="w-1 p-2 text-right">Planted</th>
                  <th className="w-1 p-2 text-right">Harvested </th>
                  <th className="w-1 p-2 text-right">Thrown Out</th>
                  <th className="w-1 whitespace-nowrap p-2 text-right">
                    To Schedule
                  </th>
                  <th>Expected Percentage</th>
                  <th>&nbsp;</th>
                </tr>
              </thead>
              <tbody>
                {order?.varieties.map((variety) => (
                  <tr key={variety.name}>
                    <td className="w-1 whitespace-nowrap p-2 text-left">
                      {variety.name}
                    </td>
                    <td className="w-1 p-2 text-right">
                      {formatNumber(variety.pots, '0,0')}
                    </td>
                    <td className="w-1 p-2 text-right">
                      {formatNumber(getVarietyHarvestedCount(variety.name), '0,0')}
                    </td>
                    <td className="w-1 p-2 text-right">
                      {formatNumber(getVarietyThrownOutCount(variety.name), '0,0')}
                    </td>
                    <td className="w-1 p-2 text-right">
                      {formatNumber(getVarietyRemainingPots(variety.name), '0,0')}
                    </td>
                    <td className="w-1 p-2 text-right">
                      <input
                        type="number"
                        value={getExpectedHarvestPercentage(variety.name)}
                        onChange={(e) => handleExpectedHarvestPercentageChange(e, variety.name)}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        min="0"
                        max="100"
                        step="1"
                      />
                    </td>
                    <td className="text-center">
                      <HeadlessUI.Switch
                        onChange={(checked) =>
                          handleSelectVarietyChanged(checked, variety.name)
                        }
                        className={classNames(
                          selectedVarieties?.[variety.name] ? 'bg-blue-400' : 'bg-gray-200',
                          'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                        )}
                      >
                        <span
                          aria-hidden="true"
                          className={classNames(
                            selectedVarieties?.[variety.name] ? 'translate-x-5' : 'translate-x-0',
                            'pointer-events-none inline-block size-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                          )}
                        />
                      </HeadlessUI.Switch>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="mt-4 grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-500">
                Crew Size
              </label>
              <input
                type="number"
                value={crewSize}
                onChange={(e) => setCrewSize(e.target.valueAsNumber)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            <div className="col-span-3">
              <label className="block text-sm font-medium text-gray-500">
                Comments
              </label>
              <textarea
                rows={3}
                value={comments ?? ''}
                onChange={(e) => setComments(e.target.value)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
