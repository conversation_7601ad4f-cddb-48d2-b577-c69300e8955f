import { useMemo, useRef, useState, Fragment } from 'react';
import { useDrop } from 'react-dnd';
import * as HeadlessUI from '@headlessui/react';
//import { useAddHarvestingOrderToHarvestingScheduleMutation } from 'api/boekestyn-harvesting-service';
import * as models from 'api/models/boekestyns';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { ProblemDetails } from '@/utils/problem-details';
import {
  selectSchedules,
  ScheduleHarvestingOrderType,
} from './harvesting-slice';
import { selectStartDate, selectEndDate } from './admin-slice';
import { HarvestingScheduleWorkOrder } from './harvesting-schedule-work-order';
import { useAddHarvestingOrderToHarvestingScheduleMutation } from 'api/boekestyn-harvesting-service';
import { DateTime } from 'luxon';
import moment from 'moment';
import { HarvestingWorkOrderDay } from './harvesting-work-order-round';

interface HarvestingScheduleProps {
  line: models.HarvestingLine;
  date: string;
}

export function HarvestingSchedule({ line, date }: HarvestingScheduleProps) {
  const //[addOrderToSchedule] = useAddHarvestingOrderToHarvestingScheduleMutation(),
    [showDetailDialog, setShowDetailDialog] =
      useState<models.HarvestingOrder | null>(null),
    [crewSize, setCrewSize] = useState(1),
    [finalRound, setFinalRound] = useState(false),
    [comments, setComments] = useState<string | null>(null),
    [error, setError] = useState<ProblemDetails | null>(null),
    [selectedVarieties, setSelectedVarieties] = useState<{[key: string]: boolean} | null>(null),
    [nextHarvestDate, setNextHarvestDate] = useState<string | null>(DateTime.now().toFormat('yyyy-MM-dd')),
    schedules = useAppSelector(selectSchedules),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    //[workOrderDates, setWorkOrderDates] = useState<string[]>([DateTime.now().toFormat('yyyy-MM-dd')]),
    [workOrderDate, setWorkOrderDate] = useState<string>(DateTime.now().toFormat('yyyy-MM-dd')),
    [showDatePicker, setShowDatePicker] = useState(false),
    [workOrdersToSchedule, setWorkOrdersToSchedule] = useState<{[key:string]: Partial<models.HarvestingWorkOrder>}>({[DateTime.now().toFormat('yyyy-MM-dd')]: {} }),

    [addOrderToSchedule] = useAddHarvestingOrderToHarvestingScheduleMutation(),
    schedule = useMemo(
      () =>
        schedules.find((s) => s.lineId === line.id && s.date === date) ?? {
          id: 0,
          lineId: line.id,
          date,
          workOrders: [],
        },
      [schedules, line.id, date]
    ),
    totalPots = useMemo(
      () => schedule.workOrders.reduce((total, o) => total + o.pots, 0) ?? 0,
      [schedule]
    ),
    estimatedHours = useMemo(
      () =>
        schedule.workOrders.reduce((total, o) => total + o.estimatedHours, 0) ??
        0,
      [schedule]
    ),
    [{ isOver }, drop] = useDrop<
      models.HarvestingOrder,
      void,
      { isOver: boolean }
    >(() => ({
      accept: ScheduleHarvestingOrderType,
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
      drop(order: models.HarvestingOrder) {
        setError(null);
        setShowDetailDialog(order);
        setSelectedVarieties(order.varieties.reduce((acc, v) => ({ ...acc, [v.name]: true }), {}));
      },
    })),
    ref = useRef<HTMLDivElement>(null);

  const handleDetailCancel = () => {
    setShowDetailDialog(null);
  };

  const handleDetailConfirm = async () => {
    if (showDetailDialog) {
      await addOrderToSchedule({
        crewSize,
        comments,
        schedule,
        order: showDetailDialog,
        varieties: showDetailDialog.varieties.filter((v) => selectedVarieties?.[v.name]),
        maximumHarvestRounds: 1,
        daysBetweenHarvestRounds: 1,
        nextRoundDate: nextHarvestDate,
        finalRound,
      }).unwrap();
      setShowDetailDialog(null);
      setCrewSize(1);
      setComments(null);
      setSelectedVarieties(null);
      setFinalRound(false);
      setNextHarvestDate(DateTime.now().toFormat('yyyy-MM-dd'));
    }
  };

  const handleDialogAfterEnter = () => {
    // var orderPots = showDetailDialog?.pots ?? 0,
    //   partiallySpaced = showDetailDialog?.potsPartiallySpaced ?? 0,
    //   fullySpaced = showDetailDialog?.potsFullySpaced ?? 0,
    //   potsToSpace = Math.max(orderPots - partiallySpaced - fullySpaced, 0),
    //   from: models.HarvestingTypes = partiallySpaced > 0 ? 'Partial' : 'Tight',
    //   to: models.SpacingTypes =
    //     showDetailDialog?.hasPartialSpace &&
    //     partiallySpaced == 0 &&
    //     from !== 'Partial'
    //       ? 'Partial'
    //       : 'Full';
    // setPotsToSpace(potsToSpace);
    // setFromSpaceType(from);
    // setToSpaceType(to);
    // setRequiresPinching(!!showDetailDialog?.hasPinching);
    // setComments(null);
  };

  drop(ref);
  function handleAddWorkOrderDate(event: React.MouseEvent<HTMLButtonElement, MouseEvent>): void {
    // Only add if the date doesn't already exist
    if (!workOrdersToSchedule.hasOwnProperty(workOrderDate)) {
      //setworkOrdersToSchedule([...workOrdersToSchedule, { date: workOrderDate }].sort((a, b) => (a.date ?? '').localeCompare(b.date ?? '')));
      setWorkOrdersToSchedule({ ...workOrdersToSchedule, [workOrderDate]: {  } });
    }
    setShowDatePicker(false);
  }

  function handleRemoveWorkOrderDate(date: string): void {
    // Don't allow removing the last tab
    if (Object.keys(workOrdersToSchedule).length > 1) {
      setWorkOrdersToSchedule(
        Object.fromEntries(
          Object.entries(workOrdersToSchedule).filter(([d]) => d !== date)
        )
      );
    }
  }

  return (
    <div
      ref={ref}
      className={classNames(
        'm-2 rounded border p-2',
        isOver && 'border-green-600'
      )}
    >
      <h2 className="text-lg font-semibold">{line.name}</h2>
      <div className="flex-grow">
        {!!schedule.id && (
          <table className="min-w-full divide-y divide-gray-300 text-sm">
            <thead>
              <tr className="sticky top-0 z-10">
                <th className="whitespace-nowrap bg-gray-100 p-2 text-left">
                  Lot #
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-left">
                  Size / Plant
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-center">
                  Harvesting
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  Pots
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  Hours
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  &nbsp;
                </th>
              </tr>
            </thead>
            <tbody>
              {schedule.workOrders.map((order) => (
                <HarvestingScheduleWorkOrder
                  key={order.id}
                  order={order}
                  scheduleId={schedule.id}
                />
              ))}
            </tbody>
            <tfoot>
              <tr>
                <th colSpan={4} className="p-2 text-right">
                  Total Hours:
                </th>
                <th className="p-2 text-right">{formatNumber(totalPots)}</th>
                <th className="p-2 text-right">
                  {formatNumber(estimatedHours, '0,0.0')}
                </th>
                <th className="p-2 text-right">&nbsp;</th>
              </tr>
            </tfoot>
          </table>
        )}
        {!schedule.id && (
          <div className="flex h-24 items-center justify-center text-gray-500">
            <span className="text-sm italic">Drag to schedule orders.</span>
          </div>
        )}
      </div>
      <HeadlessUI.Transition.Root
        show={!!showDetailDialog}
        as={Fragment}
        afterEnter={handleDialogAfterEnter}
      >
        <HeadlessUI.Dialog
          as="div"
          className="relative z-30"
          onClose={() => setShowDetailDialog(null)}
          open={!!showDetailDialog}
        >
          <HeadlessUI.Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </HeadlessUI.Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-0 text-center">
              <HeadlessUI.Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-0 scale-95"
                enterTo="opacity-100 translate-y-0 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 scale-100"
                leaveTo="opacity-0 translate-y-0 scale-95"
              >
                <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-2xl transform overflow-hidden rounded-lg bg-white p-4 text-left shadow-xl transition-all">
                  <HeadlessUI.Tab.Group>
                    <HeadlessUI.Tab.List
                      as="nav"
                      className="flex w-full flex-row overflow-x-auto border-b"
                    >
                     {Object.keys(workOrdersToSchedule).map((date) => (
                      <HeadlessUI.Tab key={date}>
                        {({ selected }) => (
                          <>
                          <button
                            type="button"
                            className={classNames(
                              'whitespace-nowrap border-b-2 px-1 py-2 text-xl font-medium',
                              selected
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                            )}
                          >
                            {moment(date).format('ddd')}
                            <div className="text-xs italic">
                              {moment(date).format('MMM D')}
                            </div>
                          </button>

                          <button
                            type="button"
                            className="btn-secondary ml-2 "
                            onClick={() => handleRemoveWorkOrderDate(date!)}
                          >
                            <Icon icon="x" className="text-red-500" />
                          </button>
                          </>
                        )}
                      </HeadlessUI.Tab>
                     ))}
                     <button
                        type="button"
                        className="btn-secondary"
                        onClick={() => setShowDatePicker(true)}
                      >
                        <Icon icon="plus" />
                      </button>
                    </HeadlessUI.Tab.List>

                    {showDatePicker && (
                      <div className="mt-4 border-t pt-4">
                        <div className="flex items-center gap-4">
                          <div className="flex-1">
                            <label className="block text-sm font-medium text-gray-500">
                              Add Date
                            </label>
                            <input
                              type="date"
                              value={workOrderDate}
                              onChange={(e) => setWorkOrderDate(e.target.value)}
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                          </div>
                          <div className="flex gap-2">
                            <button
                              type="button"
                              className="btn-secondary"
                              onClick={() => setShowDatePicker(false)}
                            >
                              Cancel
                            </button>
                            <button
                              type="button"
                              className="btn-primary"
                              onClick={handleAddWorkOrderDate}
                            >
                              Add
                            </button>
                          </div>
                        </div>
                      </div>
                    )}

                    <HeadlessUI.Tab.Panels
                      as="div"
                      className="flex flex-grow flex-col overflow-y-auto border-t"
                    >
                      {Object.keys(workOrdersToSchedule).map((date) => (
                        <HeadlessUI.Tab.Panel
                          key={date}
                          className="mt-4"
                        >
                          <HarvestingWorkOrderDay key={date} workOrder={workOrdersToSchedule[date]} allWorkOrders={workOrdersToSchedule} order={showDetailDialog} date={date} />
                        </HeadlessUI.Tab.Panel>
                      ))}
                    </HeadlessUI.Tab.Panels>
                  </HeadlessUI.Tab.Group>
                </HeadlessUI.Dialog.Panel>
              </HeadlessUI.Transition.Child>
            </div>
          </div>
        </HeadlessUI.Dialog>
      </HeadlessUI.Transition.Root>

    </div>
  );
}
